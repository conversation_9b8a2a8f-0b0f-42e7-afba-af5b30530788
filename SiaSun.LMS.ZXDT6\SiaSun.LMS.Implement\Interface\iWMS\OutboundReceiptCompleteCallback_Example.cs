using System;
using System.Collections.Generic;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 出库单完成上报回调接口使用示例
    /// </summary>
    public static class OutboundReceiptCompleteCallback_Example
    {
        /// <summary>
        /// 示例1：单个出库明细上报
        /// </summary>
        public static void ExampleSingleItem()
        {
            try
            {
                // 创建回调接口实例
                var callback = new OutboundReceiptCompleteCallback();

                // 调用接口
                bool success = callback.IntefaceMethod(
                    goodsNum: 50,                           // 数量
                    warehouseCode: "WH001",                 // 仓库编码
                    shelfCode: "SH001-A01",                 // 货架编码
                    oId: "OUTBOUND_DETAIL_001",             // 出库明细原ID
                    lId: "STEREO_WH_OUTBOUND_001",          // 立体仓系统出库明细ID
                    outboundType: 63,                       // 出库类型：63=出库单
                    out string message);

                if (success)
                {
                    Console.WriteLine($"✓ 单个出库明细上报成功：{message}");
                    // 这里可以更新本地数据库状态等后续处理
                }
                else
                {
                    Console.WriteLine($"✗ 单个出库明细上报失败：{message}");
                    // 这里可以记录失败日志，进行重试等处理
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 单个出库明细上报异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 示例2：批量出库明细上报
        /// </summary>
        public static void ExampleBatchItems()
        {
            try
            {
                // 创建回调接口实例
                var callback = new OutboundReceiptCompleteCallback();

                // 准备批量出库明细数据
                var items = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>
                {
                    // 普通出库单明细
                    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = 100,
                        warehouseCode = "WH001",
                        shelfCode = "SH001-A01",
                        oId = "OUTBOUND_DETAIL_001",
                        lId = "STEREO_WH_OUTBOUND_001",
                        outboundType = 63    // 63=出库单
                    },
                    // 借用出库明细
                    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = 25,
                        warehouseCode = "WH002",
                        shelfCode = "SH002-B01",
                        oId = "BORROW_DETAIL_001",
                        lId = "STEREO_WH_BORROW_001",
                        outboundType = 74    // 74=借用
                    },
                    // 入库红冲单明细
                    new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = 15,
                        warehouseCode = "WH003",
                        shelfCode = "SH003-C01",
                        oId = "REVERSAL_DETAIL_001",
                        lId = "STEREO_WH_REVERSAL_001",
                        outboundType = 72    // 72=入库红冲单
                    }
                };

                // 调用接口
                bool success = callback.IntefaceMethod(items, out string message);

                if (success)
                {
                    Console.WriteLine($"✓ 批量出库明细上报成功：{message}");
                    Console.WriteLine($"  共上报 {items.Count} 条明细记录");
                    
                    // 这里可以批量更新本地数据库状态
                    foreach (var item in items)
                    {
                        Console.WriteLine($"  - 明细ID: {item.lId}, 数量: {item.goodsNum}, 类型: {GetOutboundTypeName(item.outboundType)}");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ 批量出库明细上报失败：{message}");
                    // 这里可以记录失败的明细，进行重试等处理
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 批量出库明细上报异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 示例3：完整的出库单完成上报流程
        /// </summary>
        /// <param name="outboundReceiptId">出库单ID</param>
        public static void ExampleCompleteOutboundReceipt(string outboundReceiptId)
        {
            try
            {
                Console.WriteLine($"开始处理出库单 {outboundReceiptId} 的完成上报...");

                // 1. 从数据库获取出库单明细信息
                var outboundDetails = GetOutboundReceiptDetails(outboundReceiptId);
                if (outboundDetails == null || outboundDetails.Count == 0)
                {
                    Console.WriteLine($"✗ 出库单 {outboundReceiptId} 没有找到明细信息");
                    return;
                }

                // 2. 转换为上报格式
                var reportItems = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>();
                foreach (var detail in outboundDetails)
                {
                    reportItems.Add(new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = detail.ActualOutboundQuantity,
                        warehouseCode = detail.WarehouseCode,
                        shelfCode = detail.ShelfCode,
                        oId = detail.OriginalDetailId,
                        lId = detail.StereoWarehouseDetailId,
                        outboundType = detail.OutboundType
                    });
                }

                // 3. 调用上报接口
                var callback = new OutboundReceiptCompleteCallback();
                bool success = callback.IntefaceMethod(reportItems, out string message);

                if (success)
                {
                    Console.WriteLine($"✓ 出库单 {outboundReceiptId} 完成上报成功");
                    
                    // 4. 更新本地状态
                    UpdateOutboundReceiptStatus(outboundReceiptId, "已上报");
                    
                    // 5. 记录操作日志
                    LogOperation($"出库单 {outboundReceiptId} 完成上报成功，共 {reportItems.Count} 条明细");
                }
                else
                {
                    Console.WriteLine($"✗ 出库单 {outboundReceiptId} 完成上报失败：{message}");
                    
                    // 记录失败日志，可能需要重试
                    LogError($"出库单 {outboundReceiptId} 完成上报失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 处理出库单完成上报异常：{ex.Message}");
                LogError($"出库单 {outboundReceiptId} 完成上报异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取出库类型名称
        /// </summary>
        private static string GetOutboundTypeName(int outboundType)
        {
            switch (outboundType)
            {
                case 63: return "出库单";
                case 72: return "入库红冲单";
                case 74: return "借用";
                default: return $"未知类型({outboundType})";
            }
        }

        // 模拟数据库操作方法（实际项目中需要根据具体的数据访问层实现）
        
        /// <summary>
        /// 模拟：从数据库获取出库单明细
        /// </summary>
        private static List<OutboundReceiptDetail> GetOutboundReceiptDetails(string outboundReceiptId)
        {
            // 这里应该是实际的数据库查询逻辑
            // 返回模拟数据用于示例
            return new List<OutboundReceiptDetail>
            {
                new OutboundReceiptDetail
                {
                    OriginalDetailId = "DETAIL_001",
                    StereoWarehouseDetailId = "STEREO_001",
                    ActualOutboundQuantity = 100,
                    WarehouseCode = "WH001",
                    ShelfCode = "SH001-A01",
                    OutboundType = 63
                }
            };
        }

        /// <summary>
        /// 模拟：更新出库单状态
        /// </summary>
        private static void UpdateOutboundReceiptStatus(string outboundReceiptId, string status)
        {
            // 这里应该是实际的数据库更新逻辑
            Console.WriteLine($"  更新出库单 {outboundReceiptId} 状态为：{status}");
        }

        /// <summary>
        /// 模拟：记录操作日志
        /// </summary>
        private static void LogOperation(string message)
        {
            // 这里应该是实际的日志记录逻辑
            Console.WriteLine($"  操作日志：{message}");
        }

        /// <summary>
        /// 模拟：记录错误日志
        /// </summary>
        private static void LogError(string message)
        {
            // 这里应该是实际的错误日志记录逻辑
            Console.WriteLine($"  错误日志：{message}");
        }

        /// <summary>
        /// 模拟的出库单明细数据结构
        /// </summary>
        private class OutboundReceiptDetail
        {
            public string OriginalDetailId { get; set; }
            public string StereoWarehouseDetailId { get; set; }
            public int ActualOutboundQuantity { get; set; }
            public string WarehouseCode { get; set; }
            public string ShelfCode { get; set; }
            public int OutboundType { get; set; }
        }
    }
}
