using System;
using System.Collections.Generic;

namespace SiaSun.LMS.Implement.Interface.iWMS
{
    /// <summary>
    /// 入库单完成上报回调接口使用示例
    /// </summary>
    public class InboundReceiptCompleteCallbackExample
    {
        /// <summary>
        /// 示例1：单个入库明细上报
        /// </summary>
        public static void ExampleSingleItem()
        {
            try
            {
                // 创建回调接口实例
                var callback = new InboundReceiptCompleteCallback();

                // 模拟入库完成数据
                int storageNum = 100;                           // 本次入库数量
                string warehouseCode = "WH001";                 // 仓库编码
                string shelfCode = "SH001";                     // 货架编码
                string oId = "INBOUND_DETAIL_001";              // 入库明细原ID
                string lId = "STEREO_WH_DETAIL_001";            // 立体仓系统入库明细ID
                int storageType = 28;                           // 入库类型：28=入库单

                // 调用接口
                bool success = callback.IntefaceMethod(
                    storageNum, warehouseCode, shelfCode, oId, lId, storageType, 
                    out string message);

                if (success)
                {
                    Console.WriteLine($"✓ 单个入库明细上报成功：{message}");
                    // 这里可以更新本地数据库状态等后续处理
                }
                else
                {
                    Console.WriteLine($"✗ 单个入库明细上报失败：{message}");
                    // 这里可以记录失败日志，进行重试等处理
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 单个入库明细上报异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 示例2：批量入库明细上报
        /// </summary>
        public static void ExampleBatchItems()
        {
            try
            {
                // 创建回调接口实例
                var callback = new InboundReceiptCompleteCallback();

                // 模拟批量入库完成数据
                var items = new List<InboundReceiptCompleteCallback.InboundReceiptCompleteItem>
                {
                    // 普通入库单明细
                    new InboundReceiptCompleteCallback.InboundReceiptCompleteItem
                    {
                        storageNum = 100,
                        warehouseCode = "WH001",
                        shelfCode = "SH001-A01",
                        oId = "INBOUND_DETAIL_001",
                        lId = "STEREO_WH_DETAIL_001",
                        storageType = 28    // 28=入库单
                    },
                    // 另一个入库单明细
                    new InboundReceiptCompleteCallback.InboundReceiptCompleteItem
                    {
                        storageNum = 50,
                        warehouseCode = "WH001",
                        shelfCode = "SH001-A02",
                        oId = "INBOUND_DETAIL_002",
                        lId = "STEREO_WH_DETAIL_002",
                        storageType = 28    // 28=入库单
                    },
                    // 出库红冲单明细
                    new InboundReceiptCompleteCallback.InboundReceiptCompleteItem
                    {
                        storageNum = 25,
                        warehouseCode = "WH002",
                        shelfCode = "SH002-B01",
                        oId = "REVERSAL_DETAIL_001",
                        lId = "STEREO_WH_REVERSAL_001",
                        storageType = 73    // 73=出库红冲单
                    }
                };

                // 调用接口
                bool success = callback.IntefaceMethod(items, out string message);

                if (success)
                {
                    Console.WriteLine($"✓ 批量入库明细上报成功：{message}");
                    Console.WriteLine($"  共上报 {items.Count} 条明细记录");
                    
                    // 这里可以批量更新本地数据库状态
                    foreach (var item in items)
                    {
                        Console.WriteLine($"  - 明细ID: {item.lId}, 数量: {item.storageNum}, 类型: {GetStorageTypeName(item.storageType)}");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ 批量入库明细上报失败：{message}");
                    // 这里可以记录失败的明细，进行重试等处理
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 批量入库明细上报异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 示例3：在业务流程中的实际应用
        /// </summary>
        /// <param name="inboundReceiptId">入库单ID</param>
        public static void ExampleInBusinessProcess(string inboundReceiptId)
        {
            try
            {
                Console.WriteLine($"开始处理入库单完成上报，入库单ID：{inboundReceiptId}");

                // 1. 从数据库查询入库单明细
                var inboundDetails = GetInboundReceiptDetails(inboundReceiptId);
                if (inboundDetails == null || inboundDetails.Count == 0)
                {
                    Console.WriteLine("未找到入库单明细数据");
                    return;
                }

                // 2. 转换为上报格式
                var reportItems = new List<InboundReceiptCompleteCallback.InboundReceiptCompleteItem>();
                foreach (var detail in inboundDetails)
                {
                    reportItems.Add(new InboundReceiptCompleteCallback.InboundReceiptCompleteItem
                    {
                        storageNum = detail.ActualQuantity,
                        warehouseCode = detail.WarehouseCode,
                        shelfCode = detail.ShelfCode,
                        oId = detail.OriginalDetailId,
                        lId = detail.StereoWarehouseDetailId,
                        storageType = 28  // 假设都是入库单类型
                    });
                }

                // 3. 调用上报接口
                var callback = new InboundReceiptCompleteCallback();
                bool success = callback.IntefaceMethod(reportItems, out string message);

                if (success)
                {
                    Console.WriteLine($"✓ 入库单 {inboundReceiptId} 完成上报成功");
                    
                    // 4. 更新本地状态
                    UpdateInboundReceiptStatus(inboundReceiptId, "已上报");
                    
                    // 5. 记录操作日志
                    LogOperation($"入库单 {inboundReceiptId} 完成上报成功，共 {reportItems.Count} 条明细");
                }
                else
                {
                    Console.WriteLine($"✗ 入库单 {inboundReceiptId} 完成上报失败：{message}");
                    
                    // 记录失败日志，可能需要重试
                    LogError($"入库单 {inboundReceiptId} 完成上报失败：{message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 处理入库单完成上报异常：{ex.Message}");
                LogError($"入库单 {inboundReceiptId} 完成上报异常：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取入库类型名称
        /// </summary>
        private static string GetStorageTypeName(int storageType)
        {
            switch (storageType)
            {
                case 28: return "入库单";
                case 73: return "出库红冲单";
                case 75: return "归还单";
                default: return "未知类型";
            }
        }

        // 模拟数据访问方法
        private static List<InboundReceiptDetail> GetInboundReceiptDetails(string inboundReceiptId)
        {
            // 这里应该是实际的数据库查询逻辑
            return new List<InboundReceiptDetail>
            {
                new InboundReceiptDetail
                {
                    OriginalDetailId = "DETAIL_001",
                    StereoWarehouseDetailId = "SW_DETAIL_001",
                    ActualQuantity = 100,
                    WarehouseCode = "WH001",
                    ShelfCode = "SH001-A01"
                }
            };
        }

        private static void UpdateInboundReceiptStatus(string inboundReceiptId, string status)
        {
            // 这里应该是实际的数据库更新逻辑
            Console.WriteLine($"更新入库单 {inboundReceiptId} 状态为：{status}");
        }

        private static void LogOperation(string message)
        {
            // 这里应该是实际的日志记录逻辑
            S_Base.sBase.Log.Info($"[入库单完成上报] {message}");
        }

        private static void LogError(string message)
        {
            // 这里应该是实际的错误日志记录逻辑
            S_Base.sBase.Log.Error($"[入库单完成上报] {message}");
        }

        // 模拟入库单明细数据结构
        private class InboundReceiptDetail
        {
            public string OriginalDetailId { get; set; }
            public string StereoWarehouseDetailId { get; set; }
            public int ActualQuantity { get; set; }
            public string WarehouseCode { get; set; }
            public string ShelfCode { get; set; }
        }
    }
}
